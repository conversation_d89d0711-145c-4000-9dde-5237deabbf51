'use client'

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from 'react'
import { listUserTenants, listUserEntities } from '@ledgerly/dal'
import type { Database } from '@ledgerly/types'
import type { UserContextResponse } from '@/lib/api-types'

type UserTenant = Database['public']['Views']['v_user_tenants']['Row']
type UserEntity = Database['public']['Views']['v_user_entities']['Row']

interface OrgEntitySelection {
  tenantId: number | null
  entityId: number | null
  tenantName?: string
  entityName?: string
  orgType?: 'sme' | 'firm'
  mode: 'tenant' | 'entity'
}

interface OrgEntityContextType {
  selection: OrgEntitySelection
  tenants: UserTenant[]
  entities: UserEntity[]
  loading: boolean
  error: string | null

  // Context data
  userContext: UserContextResponse | null

  // UX helpers
  isSME: boolean
  isFirm: boolean
  shouldShowEntitySelector: boolean
  shouldShowOrgSelector: boolean

  // Actions
  selectTenant: (tenant: UserTenant | null) => void
  selectEntity: (entity: UserEntity | null) => void
  refreshData: () => Promise<void>
}

const OrgEntityContext = createContext<OrgEntityContextType | undefined>(
  undefined
)

const STORAGE_KEY = 'ledgerly_org_entity_selection'

function getStoredSelection(): OrgEntitySelection | null {
  if (typeof window === 'undefined') return null

  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return stored ? JSON.parse(stored) : null
  } catch {
    return null
  }
}

// Fetch user context from the new API endpoint
async function fetchUserContext(): Promise<UserContextResponse | null> {
  try {
    // Get CSRF token from meta tag or cookie
    let csrfToken = ''

    // Try meta tag first
    const metaElement = document.querySelector('meta[name="csrf-token"]')
    if (metaElement) {
      csrfToken = metaElement.getAttribute('content') || ''
    }

    // If no meta tag, try cookie
    if (!csrfToken) {
      const cookies = document.cookie.split(';')
      const csrfCookie = cookies.find(cookie => cookie.trim().startsWith('csrf-token='))
      if (csrfCookie) {
        csrfToken = csrfCookie.split('=')[1]
      }
    }

    const headers: Record<string, string> = {}
    if (csrfToken) {
      headers['X-CSRF-Token'] = csrfToken
    }

    const response = await fetch('/api/me/context', {
      method: 'GET',
      headers,
    })

    if (!response.ok) {
      throw new Error('Failed to fetch user context')
    }
    return await response.json() as UserContextResponse
  } catch (error) {
    console.error('Error fetching user context:', error)
    return null
  }
}

function storeSelection(selection: OrgEntitySelection) {
  if (typeof window === 'undefined') return

  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(selection))
  } catch (error) {
    console.warn('Failed to store org/entity selection:', error)
  }
}

export function OrgEntityProvider({ children }: { children: React.ReactNode }) {
  const [selection, setSelection] = useState<OrgEntitySelection>(() => {
    return (
      getStoredSelection() || {
        tenantId: null,
        entityId: null,
        mode: 'tenant',
      }
    )
  })

  const [tenants, setTenants] = useState<UserTenant[]>([])
  const [entities, setEntities] = useState<UserEntity[]>([])
  const [userContext, setUserContext] = useState<UserContextResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const [tenantsData, entitiesData, contextData] = await Promise.all([
        listUserTenants(),
        listUserEntities(),
        fetchUserContext(),
      ])

      setTenants(tenantsData || [])
      setUserContext(contextData)

      // Use entities from userContext if available, otherwise fall back to entitiesData
      let finalEntities = entitiesData || []
      if (contextData?.entities && contextData.entities.length > 0) {
        // Convert userContext entities to the expected format
        finalEntities = contextData.entities.map(entity => ({
          user_id: null, // Not needed for display
          entity_id: entity.id,
          tenant_id: contextData.tenant?.id || null,
          role: entity.role,
          entity_name: entity.name,
          tenant_name: contextData.tenant?.name || null,
        }))
        console.log('OrgEntityContext: Using entities from userContext')
      } else {
        console.log('OrgEntityContext: Using entities from DAL')
      }

      setEntities(finalEntities)

      // Debug logging
      console.log('OrgEntityContext loaded data:', {
        tenantsCount: tenantsData?.length || 0,
        entitiesCount: finalEntities?.length || 0,
        contextTenant: contextData?.tenant?.name,
        contextOrgType: contextData?.tenant?.org_type,
        contextEntitiesCount: contextData?.entities?.length || 0,
        contextEntities: contextData?.entities?.map(e => e.name),
        finalEntitiesCount: finalEntities.length
      })

      // For SME users, auto-select the single entity
      if (contextData?.tenant?.org_type === 'sme' && contextData.entities.length > 0) {
        const smeEntity = contextData.entities[0]
        const newSelection: OrgEntitySelection = {
          tenantId: contextData.tenant.id,
          entityId: smeEntity.id,
          tenantName: contextData.tenant.name,
          entityName: smeEntity.name,
          orgType: 'sme',
          mode: 'entity',
        }
        setSelection(newSelection)
        storeSelection(newSelection)
        return
      }

      // Auto-select first tenant if none selected and user has tenants
      if (!selection.tenantId && tenantsData && tenantsData.length > 0) {
        const firstTenant = tenantsData[0]
        const newSelection: OrgEntitySelection = {
          tenantId: firstTenant.tenant_id,
          entityId: null,
          tenantName: firstTenant.tenant_name ?? undefined,
          orgType: firstTenant.org_type as 'sme' | 'firm',
          mode: 'tenant' as const,
        }
        setSelection(newSelection)
        storeSelection(newSelection)
      }

      // Validate stored selection still exists
      if (selection.tenantId) {
        const tenantExists = tenantsData?.some(
          (t: UserTenant) => t.tenant_id === selection.tenantId
        )
        if (!tenantExists) {
          const newSelection = {
            tenantId: null,
            entityId: null,
            mode: 'tenant' as const,
          }
          setSelection(newSelection)
          storeSelection(newSelection)
        }
      }

      if (selection.entityId) {
        const entityExists = entitiesData?.some(
          (e: UserEntity) => e.entity_id === selection.entityId
        )
        if (!entityExists) {
          const newSelection = {
            ...selection,
            entityId: null,
            entityName: undefined,
            mode: 'tenant' as const,
          }
          setSelection(newSelection)
          storeSelection(newSelection)
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data')
      console.error('Error loading org/entity data:', err)
    } finally {
      setLoading(false)
    }
  }, [selection])

  const selectTenant = (tenant: UserTenant | null) => {
    const newSelection: OrgEntitySelection = {
      tenantId: tenant?.tenant_id || null,
      entityId: null, // Reset entity when switching tenants
      tenantName: tenant?.tenant_name ?? undefined,
      entityName: undefined,
      orgType: tenant?.org_type as 'sme' | 'firm',
      mode: 'tenant',
    }

    setSelection(newSelection)
    storeSelection(newSelection)
  }

  const selectEntity = (entity: UserEntity | null) => {
    const tenant = tenants.find(t => t.tenant_id === entity?.tenant_id)
    const newSelection: OrgEntitySelection = {
      tenantId: entity?.tenant_id || selection.tenantId,
      entityId: entity?.entity_id || null,
      tenantName: entity?.tenant_name ?? selection.tenantName,
      entityName: entity?.entity_name ?? undefined,
      orgType: tenant?.org_type as 'sme' | 'firm',
      mode: 'entity',
    }

    setSelection(newSelection)
    storeSelection(newSelection)
  }

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    loadData()
  }, [loadData])

  // Calculate UX helpers
  const isSME = userContext?.tenant?.org_type === 'sme'
  const isFirm = userContext?.tenant?.org_type === 'firm'
  // For SME users, never show entity selector. For others, show if firm or multiple entities
  const shouldShowEntitySelector = !isSME && (isFirm || (userContext?.entities?.length || entities.length) > 1)
  const shouldShowOrgSelector = tenants.length > 1 && !isSME

  // Debug logging for UX helpers
  console.log('UX helpers calculated:', {
    isSME,
    isFirm,
    shouldShowEntitySelector,
    shouldShowOrgSelector,
    orgType: userContext?.tenant?.org_type,
    entitiesFromContext: userContext?.entities?.length || 0,
    entitiesFromDAL: entities.length
  })

  const contextValue: OrgEntityContextType = {
    selection,
    tenants,
    entities,
    userContext,
    loading,
    error,
    isSME,
    isFirm,
    shouldShowEntitySelector,
    shouldShowOrgSelector,
    selectTenant,
    selectEntity,
    refreshData: loadData,
  }

  return (
    <OrgEntityContext.Provider value={contextValue}>
      {children}
    </OrgEntityContext.Provider>
  )
}

export function useOrgEntity() {
  const context = useContext(OrgEntityContext)
  if (context === undefined) {
    throw new Error('useOrgEntity must be used within an OrgEntityProvider')
  }
  return context
}

// Convenience hooks
export function useCurrentTenant() {
  const { selection, tenants } = useOrgEntity()
  return tenants.find(t => t.tenant_id === selection.tenantId) || null
}

export function useCurrentEntity() {
  const { selection, entities } = useOrgEntity()
  return entities.find(e => e.entity_id === selection.entityId) || null
}

export function useEntitiesForTenant(tenantId: number | null) {
  const { entities } = useOrgEntity()
  return entities.filter(e => e.tenant_id === tenantId)
}
