import { useState, useEffect, useCallback } from 'react'
import { createSecureBrowserClient } from '@/lib/session-security'
import { useAuth } from '@/contexts/AuthContext'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'

const BFF_BASE_URL = process.env.NEXT_PUBLIC_BFF_URL || 'http://localhost:4000'

export interface InboxDocument {
  id: number
  entity_id: number
  path: string
  mime_type: string
  source: string
  status: string
  confidence?: number
  created_at: string
  updated_at: string
  error_msg?: string
  supplier_name?: string
  invoice_number?: string
  invoice_date?: string
  gross_amount?: string
}

export interface DocumentsPagination {
  page: number
  limit: number
  total: number
  pages: number
}

export interface DocumentsResponse {
  documents: InboxDocument[]
  pagination: DocumentsPagination
}

export interface UseInboxDocumentsOptions {
  status?: string
  page?: number
  limit?: number
}

export interface UseInboxDocumentsHook {
  documents: InboxDocument[]
  pagination: DocumentsPagination | null
  loading: boolean
  error: string | null
  refreshDocuments: () => Promise<void>
}

export const useInboxDocuments = (options: UseInboxDocumentsOptions = {}): UseInboxDocumentsHook => {
  const { user } = useAuth()
  const { currentEntity, isValid, selection } = useOrgEntitySelection()
  const [documents, setDocuments] = useState<InboxDocument[]>([])
  const [pagination, setPagination] = useState<DocumentsPagination | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { status, page = 1, limit = 10 } = options

  const fetchDocuments = useCallback(async () => {

    if (!user || !isValid || !currentEntity) {
      setDocuments([])
      setPagination(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const supabase = createSecureBrowserClient()
      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      if (!token) {
        console.error('useInboxDocuments: No authentication token available')
        throw new Error('No authentication token available')
      }

      const entityId = currentEntity.entity_id
      if (!entityId) {
        throw new Error('No entity selected')
      }

      // Build query parameters
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      })

      if (status) {
        queryParams.append('status', status)
      }

      const response = await fetch(
        `${BFF_BASE_URL}/entities/${entityId}/documents?${queryParams}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
            Authorization: `Bearer ${token}`,
          },
        }
      )

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(errorText || `API responded with ${response.status}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch documents')
      }

      const data = result.data as DocumentsResponse
      setDocuments(data.documents || [])
      setPagination(data.pagination || null)
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to load documents'
      )
      console.error('Inbox documents error:', err)
      setDocuments([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [user, isValid, currentEntity, status, page, limit])

  useEffect(() => {
    void fetchDocuments()
  }, [fetchDocuments])

  // Set up real-time subscription for document changes
  useEffect(() => {
    if (!user || !isValid || !currentEntity) return

    const supabase = createSecureBrowserClient()
    const entityId = currentEntity.entity_id

    if (!entityId) return

    // Subscribe to inbox_documents changes for this entity
    const subscription = supabase
      .channel('inbox_documents')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'inbox_documents',
        filter: `entity_id=eq.${entityId}`
      }, () => {
        // Refresh documents when changes occur
        void fetchDocuments()
      })
      .subscribe()

    return () => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe()
      }
    }
  }, [user, isValid, currentEntity, fetchDocuments])

  const refreshDocuments = async () => {
    await fetchDocuments()
  }

  return {
    documents,
    pagination,
    loading,
    error,
    refreshDocuments,
  }
}
