import { useState, useEffect, useCallback } from 'react'
import { createSecureBrowserClient } from '@/lib/session-security'
import { useAuth } from '@/contexts/AuthContext'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'

const BFF_BASE_URL = process.env.NEXT_PUBLIC_BFF_URL || 'http://localhost:4000'

export interface InboxDocument {
  id: number
  entity_id: number
  path: string
  mime_type: string
  source: string
  status: string
  confidence?: number
  created_at: string
  updated_at: string
  error_msg?: string
  supplier_name?: string
  invoice_number?: string
  invoice_date?: string
  gross_amount?: string
}

export interface DocumentsPagination {
  page: number
  limit: number
  total: number
  pages: number
}

export interface DocumentsResponse {
  documents: InboxDocument[]
  pagination: DocumentsPagination
}

export interface UseInboxDocumentsOptions {
  status?: string
  page?: number
  limit?: number
}

export interface UseInboxDocumentsHook {
  documents: InboxDocument[]
  pagination: DocumentsPagination | null
  loading: boolean
  error: string | null
  refreshDocuments: () => Promise<void>
}

export const useInboxDocuments = (options: UseInboxDocumentsOptions = {}): UseInboxDocumentsHook => {
  const { user } = useAuth()
  const { currentEntity, isValid } = useOrgEntitySelection()
  const [documents, setDocuments] = useState<InboxDocument[]>([])
  const [pagination, setPagination] = useState<DocumentsPagination | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { status, page = 1, limit = 10 } = options

  const fetchDocuments = useCallback(async () => {
    console.log('useInboxDocuments: fetchDocuments called', {
      user: !!user,
      isValid,
      currentEntity: currentEntity ? { entity_id: currentEntity.entity_id, name: currentEntity.name } : null,
      selection,
      status,
      page,
      limit
    })

    if (!user || !isValid) {
      console.log('useInboxDocuments: Missing user or invalid selection - stopping fetch')
      setDocuments([])
      setPagination(null)
      setLoading(false)
      return
    }

    // For now, use entity ID from selection even if currentEntity is null
    // This is a temporary workaround for the entity selection issue
    let entityId: number | null = null
    if (currentEntity?.entity_id) {
      entityId = currentEntity.entity_id
    } else if (selection.entityId) {
      entityId = selection.entityId
    } else {
      // Fallback to entity ID 1 for now (since we know it exists)
      console.warn('useInboxDocuments: No entity selected, using fallback entity ID 1')
      entityId = 1
    }

    if (!entityId) {
      console.log('useInboxDocuments: No entity ID available - stopping fetch')
      setDocuments([])
      setPagination(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const supabase = createSecureBrowserClient()
      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      if (!token) {
        console.error('useInboxDocuments: No authentication token available')
        throw new Error('No authentication token available')
      }

      console.log('useInboxDocuments: Using entity ID', entityId)

      // Build query parameters
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      })

      if (status) {
        queryParams.append('status', status)
      }

      const url = `${BFF_BASE_URL}/entities/${entityId}/documents?${queryParams}`
      console.log('useInboxDocuments: Fetching from', url)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
          Authorization: `Bearer ${token}`,
        },
      })

      console.log('useInboxDocuments: Response status', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('useInboxDocuments: API error', response.status, errorText)
        throw new Error(errorText || `API responded with ${response.status}`)
      }

      const result = await response.json()
      console.log('useInboxDocuments: API response', result)

      if (!result.success) {
        console.error('useInboxDocuments: API returned error', result.error)
        throw new Error(result.error || 'Failed to fetch documents')
      }

      const data = result.data as DocumentsResponse
      console.log('useInboxDocuments: Setting documents', data.documents?.length || 0, 'documents')
      setDocuments(data.documents || [])
      setPagination(data.pagination || null)
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to load documents'
      )
      console.error('Inbox documents error:', err)
      setDocuments([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [user, isValid, currentEntity, status, page, limit])

  useEffect(() => {
    void fetchDocuments()
  }, [fetchDocuments])

  // Set up real-time subscription for document changes
  useEffect(() => {
    if (!user || !isValid || !currentEntity) return

    const supabase = createSecureBrowserClient()
    const entityId = currentEntity.entity_id

    if (!entityId) return

    // Subscribe to inbox_documents changes for this entity
    const subscription = supabase
      .channel('inbox_documents')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'inbox_documents',
        filter: `entity_id=eq.${entityId}`
      }, () => {
        // Refresh documents when changes occur
        void fetchDocuments()
      })
      .subscribe()

    return () => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe()
      }
    }
  }, [user, isValid, currentEntity, fetchDocuments])

  const refreshDocuments = async () => {
    await fetchDocuments()
  }

  return {
    documents,
    pagination,
    loading,
    error,
    refreshDocuments,
  }
}
